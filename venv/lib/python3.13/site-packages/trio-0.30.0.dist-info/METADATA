Metadata-Version: 2.4
Name: trio
Version: 0.30.0
Summary: A friendly Python library for async concurrency and I/O
Author-email: "<PERSON>" <<EMAIL>>
License-Expression: MIT OR Apache-2.0
Project-URL: Homepage, https://github.com/python-trio/trio
Project-URL: Documentation, https://trio.readthedocs.io/
Project-URL: Changelog, https://trio.readthedocs.io/en/latest/history.html
Keywords: async,io,networking,trio
Classifier: Development Status :: 4 - Beta
Classifier: Framework :: Trio
Classifier: Intended Audience :: Developers
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: POSIX :: BSD
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: System :: Networking
Classifier: Typing :: Typed
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
License-File: LICENSE.APACHE2
License-File: LICENSE.MIT
Requires-Dist: attrs>=23.2.0
Requires-Dist: sortedcontainers
Requires-Dist: idna
Requires-Dist: outcome
Requires-Dist: sniffio>=1.3.0
Requires-Dist: cffi>=1.14; os_name == "nt" and implementation_name != "pypy"
Requires-Dist: exceptiongroup; python_version < "3.11"
Dynamic: license-file

.. image:: https://img.shields.io/badge/chat-join%20now-blue.svg
   :target: https://gitter.im/python-trio/general
   :alt: Join chatroom

.. image:: https://img.shields.io/badge/forum-join%20now-blue.svg
   :target: https://trio.discourse.group
   :alt: Join forum

.. image:: https://img.shields.io/badge/docs-read%20now-blue.svg
   :target: https://trio.readthedocs.io
   :alt: Documentation

.. image:: https://img.shields.io/pypi/v/trio.svg
   :target: https://pypi.org/project/trio
   :alt: Latest PyPi version

.. image:: https://img.shields.io/conda/vn/conda-forge/trio.svg
   :target: https://anaconda.org/conda-forge/trio
   :alt: Latest conda-forge version

.. image:: https://codecov.io/gh/python-trio/trio/branch/main/graph/badge.svg
   :target: https://codecov.io/gh/python-trio/trio
   :alt: Test coverage

Trio – a friendly Python library for async concurrency and I/O
==============================================================

.. image:: https://raw.githubusercontent.com/python-trio/trio/9b0bec646a31e0d0f67b8b6ecc6939726faf3e17/logo/logo-with-background.svg
   :width: 200px
   :align: right

The Trio project aims to produce a production-quality,
`permissively licensed
<https://github.com/python-trio/trio/blob/main/LICENSE>`__,
async/await-native I/O library for Python. Like all async libraries,
its main purpose is to help you write programs that do **multiple
things at the same time** with **parallelized I/O**. A web spider that
wants to fetch lots of pages in parallel, a web server that needs to
juggle lots of downloads and websocket connections simultaneously, a
process supervisor monitoring multiple subprocesses... that sort of
thing. Compared to other libraries, Trio attempts to distinguish
itself with an obsessive focus on **usability** and
**correctness**. Concurrency is complicated; we try to make it *easy*
to get things *right*.

Trio was built from the ground up to take advantage of the `latest
Python features <https://www.python.org/dev/peps/pep-0492/>`__, and
draws inspiration from `many sources
<https://github.com/python-trio/trio/wiki/Reading-list>`__, in
particular Dave Beazley's `Curio <https://curio.readthedocs.io/>`__.
The resulting design is radically simpler than older competitors like
`asyncio <https://docs.python.org/3/library/asyncio.html>`__ and
`Twisted <https://twistedmatrix.com/>`__, yet just as capable. Trio is
the Python I/O library I always wanted; I find it makes building
I/O-oriented programs easier, less error-prone, and just plain more
fun. `Perhaps you'll find the same
<https://github.com/python-trio/trio/wiki/Testimonials>`__.

Trio is a mature and well-tested project: the overall design is solid,
and the existing features are fully documented and widely used in
production. While we occasionally make minor interface adjustments,
breaking changes are rare. We encourage you to use Trio with confidence,
but if you rely on long-term API stability, consider `subscribing to
issue #1 <https://github.com/python-trio/trio/issues/1>`__ for advance
notice of any compatibility updates.


Where to next?
--------------

**I want to try it out!** Awesome! We have a `friendly tutorial
<https://trio.readthedocs.io/en/stable/tutorial.html>`__ to get you
started; no prior experience with async coding is required.

**Ugh, I don't want to read all that – show me some code!** If you're
impatient, then here's a `simple concurrency example
<https://trio.readthedocs.io/en/stable/tutorial.html#tutorial-example-tasks-intro>`__,
an `echo client
<https://trio.readthedocs.io/en/stable/tutorial.html#tutorial-echo-client-example>`__,
and an `echo server
<https://trio.readthedocs.io/en/stable/tutorial.html#tutorial-echo-server-example>`__.

**How does Trio make programs easier to read and reason about than
competing approaches?** Trio is based on a new way of thinking that we
call "structured concurrency". The best theoretical introduction is
the article `Notes on structured concurrency, or: Go statement
considered harmful
<https://vorpus.org/blog/notes-on-structured-concurrency-or-go-statement-considered-harmful/>`__.
Or, `check out this talk at PyCon 2018
<https://www.youtube.com/watch?v=oLkfnc_UMcE>`__ to see a
demonstration of implementing the "Happy Eyeballs" algorithm in an
older library versus Trio.

**Cool, but will it work on my system?** Probably! As long as you have
some kind of Python 3.9-or-better (CPython or `currently maintained versions of
PyPy3 <https://doc.pypy.org/en/latest/faq.html#which-python-versions-does-pypy-implement>`__
are both fine), and are using Linux, macOS, Windows, or FreeBSD, then Trio
will work. Other environments might work too, but those
are the ones we test on. And all of our dependencies are pure Python,
except for CFFI on Windows, which has wheels available, so
installation should be easy (no C compiler needed).

**I tried it, but it's not working.** Sorry to hear that! You can try
asking for help in our `chat room
<https://gitter.im/python-trio/general>`__ or `forum
<https://trio.discourse.group>`__, `filing a bug
<https://github.com/python-trio/trio/issues/new>`__, or `posting a
question on StackOverflow
<https://stackoverflow.com/questions/ask?tags=python+python-trio>`__,
and we'll do our best to help you out.

**Trio is awesome, and I want to help make it more awesome!** You're
the best! There's tons of work to do – filling in missing
functionality, building up an ecosystem of Trio-using libraries,
usability testing (e.g., maybe try teaching yourself or a friend to
use Trio and make a list of every error message you hit and place
where you got confused?), improving the docs, ... check out our `guide
for contributors
<https://trio.readthedocs.io/en/stable/contributing.html>`__!

**I don't have any immediate plans to use it, but I love geeking out
about I/O library design!** That's a little weird? But let's be
honest, you'll fit in great around here. We have a `whole sub-forum
for discussing structured concurrency
<https://trio.discourse.group/c/structured-concurrency>`__ (developers
of other systems welcome!). Or check out our `discussion of design
choices
<https://trio.readthedocs.io/en/stable/design.html#user-level-api-principles>`__,
`reading list
<https://github.com/python-trio/trio/wiki/Reading-list>`__, and
`issues tagged design-discussion
<https://github.com/python-trio/trio/labels/design%20discussion>`__.

**I want to make sure my company's lawyers won't get angry at me!** No
worries, Trio is permissively licensed under your choice of MIT or
Apache 2. See `LICENSE
<https://github.com/python-trio/trio/blob/main/LICENSE>`__ for details.


Code of conduct
---------------

Contributors are requested to follow our `code of conduct
<https://trio.readthedocs.io/en/stable/code-of-conduct.html>`__ in all
project spaces.
