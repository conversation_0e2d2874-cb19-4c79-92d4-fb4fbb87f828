
import time

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

# --- Config ---
USERNAME = "uvais_mohammad___"
PASSWORD = "0000bavu****"

# --- Setup driver ---
chrome_options = webdriver.ChromeOptions()
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")
chrome_options.add_argument("--disable-blink-features=AutomationControlled")
chrome_options.add_argument("--start-maximized")  # Start in full screen
chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
chrome_options.add_experimental_option('useAutomationExtension', False)

driver = webdriver.Chrome(options=chrome_options)
driver.maximize_window()  # Ensure full screen
driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
driver.get("https://www.instagram.com/")

wait = WebDriverWait(driver, 20)

# --- Login ---
user_input = wait.until(EC.presence_of_element_located((By.NAME, "username")))
user_input.send_keys(USERNAME)

pass_input = driver.find_element(By.NAME, "password")
pass_input.send_keys(PASSWORD)
pass_input.send_keys(Keys.RETURN)

# Wait for homepage to load
time.sleep(5)
print("✅ Logged in successfully!")

# --- Navigate to Privacy & Security ---
driver.get("https://www.instagram.com/accounts/hide_story_and_live_from/")
wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

# --- List of usernames to allow (DO NOT hide story from these) ---
allowed_usernames = ["fath_wafaa_"]

# --- Time estimation setup ---
TOTAL_FOLLOWERS = 421
total_users_to_process = TOTAL_FOLLOWERS - len(allowed_usernames)
ESTIMATED_TIME_PER_USER = 2.5  # seconds (including delays and processing)

print(f"📊 Total followers: {TOTAL_FOLLOWERS}")
print(f"📊 Allowed users (skip): {len(allowed_usernames)}")
print(f"📊 Users to process: {total_users_to_process}")
print(f"⏱️ Estimated total time: {(total_users_to_process * ESTIMATED_TIME_PER_USER) / 60:.1f} minutes")
print("-" * 50)

SCROLL_PAUSE_TIME = 3  # Increased pause time for better loading
scroll_attempts = 0
max_scroll_attempts = 10  # Prevent infinite scrolling

print("🔄 Starting to scroll and load all users...")
last_height = driver.execute_script("return document.body.scrollHeight")
print("Initial height: ", last_height)

while scroll_attempts < max_scroll_attempts:
    print(f"Scrolling... (Attempt {scroll_attempts + 1}/{max_scroll_attempts})")

    # Scroll down gradually instead of jumping to bottom
    current_position = driver.execute_script("return window.pageYOffset;")
    driver.execute_script("window.scrollBy(0, 800);")  # Scroll by 800px increments
    time.sleep(SCROLL_PAUSE_TIME)

    new_height = driver.execute_script("return document.body.scrollHeight")
    new_position = driver.execute_script("return window.pageYOffset;")

    print(f"Height: {last_height} -> {new_height}, Position: {current_position} -> {new_position}")

    # Check if we've reached the bottom and no new content loaded
    if new_height == last_height and (new_position + driver.execute_script("return window.innerHeight;")) >= new_height:
        print("✅ Reached bottom of page")
        break

    last_height = new_height
    scroll_attempts += 1

# Final scroll to top to ensure all elements are properly loaded
print("📜 Scrolling back to top...")
driver.execute_script("window.scrollTo(0, 0);")
time.sleep(2)

user_buttons = driver.find_elements(By.XPATH, "//div[contains(@style, 'pointer-events: auto; width: 100%; margin-left: 4px; margin-right: 4px; cursor: pointer; -webkit-tap-highlight-color: transparent; align-items: center; flex-direction: row; justify-content: space-between;')]")   # Grabs username elements
print("user_buttons: ", user_buttons)
toggles = driver.find_elements(By.XPATH, "//div[contains(@aria-label, 'Toggle checkbox')]")
print("toggles: ", toggles)
# You may need to update the XPaths above by inspecting the DOM.
# Pair usernames and toggles (assuming same order)
processed_count = 0
hidden_count = 0
start_time = time.time()

def calculate_remaining_time(processed, hidden, start_time):
    if processed == 0:
        return total_users_to_process * ESTIMATED_TIME_PER_USER

    elapsed_time = time.time() - start_time
    avg_time_per_user = elapsed_time / processed
    remaining_users = total_users_to_process - hidden
    return remaining_users * avg_time_per_user

def format_time(seconds):
    if seconds < 60:
        return f"{seconds:.0f}s"
    elif seconds < 3600:
        return f"{seconds/60:.1f}m"
    else:
        return f"{seconds/3600:.1f}h {(seconds%3600)/60:.0f}m"

print("🚀 Starting to process users...")
try:
    for username_elem, toggle in zip(user_buttons, toggles):
        try:
            username = username_elem.text.strip()
            processed_count += 1

            # Calculate and display progress
            remaining_time = calculate_remaining_time(processed_count, hidden_count, start_time)
            progress_percent = (hidden_count / total_users_to_process) * 100 if total_users_to_process > 0 else 0

            print(f"\n👤 Processing: {username} ({processed_count}/{len(user_buttons)})")
            print(f"📈 Progress: {hidden_count}/{total_users_to_process} hidden ({progress_percent:.1f}%)")
            print(f"⏰ Estimated remaining time: {format_time(remaining_time)}")

            if username not in allowed_usernames:
                # Check the style attribute to determine if checkbox is checked or unchecked
                style_attr = toggle.get_attribute("style")

                # Style patterns for checked and unchecked states
                unchecked_pattern = 'mask-image: url("https://i.instagram.com/static/images/bloks/icons/generated/circle__outline__24-4x.png/2f71074dce25.png"); mask-size: contain; background-color: rgb(54, 54, 54); flex-shrink: 0; height: 24px; width: 24px;'
                checked_pattern = 'mask-image: url("https://i.instagram.com/static/images/bloks/icons/generated/circle-check__filled__24-4x.png/219f67ac4c95.png"); mask-size: contain; background-color: rgb(0, 149, 246); flex-shrink: 0; height: 24px; width: 24px;'

                # Determine if checkbox is checked based on style
                is_checked = "circle-check__filled" in style_attr or "rgb(0, 149, 246)" in style_attr

                # Click toggle only if not already checked
                if not is_checked:
                    try:
                        # Scroll element into view first
                        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", toggle)
                        time.sleep(0.5)  # Wait for scroll to complete

                        # Wait for element to be clickable
                        wait.until(EC.element_to_be_clickable(toggle))

                        # Try regular click first
                        try:
                            toggle.click()
                        except Exception:
                            # If regular click fails, try JavaScript click
                            print(f"⚠️ Regular click failed for {username}, trying JavaScript click...")
                            driver.execute_script("arguments[0].click();", toggle)

                        hidden_count += 1
                        print(f"✅ Hid story from: {username}")

                        # Update remaining time after successful hide
                        remaining_time = calculate_remaining_time(processed_count, hidden_count, start_time)
                        print(f"⏰ Updated remaining time: {format_time(remaining_time)}")

                        # Add small delay after each click to prevent overwhelming Instagram
                        time.sleep(1.5)

                    except Exception as click_error:
                        print(f"❌ Failed to click toggle for {username}: {str(click_error)}")
                        # Try to find and click the parent element or alternative selector
                        try:
                            parent_toggle = toggle.find_element(By.XPATH, ".//ancestor::div[@role='button'][1]")
                            driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", parent_toggle)
                            time.sleep(0.5)
                            driver.execute_script("arguments[0].click();", parent_toggle)
                            hidden_count += 1
                            print(f"✅ Hid story from: {username} (using parent element)")

                            # Update remaining time after successful hide
                            remaining_time = calculate_remaining_time(processed_count, hidden_count, start_time)
                            print(f"⏰ Updated remaining time: {format_time(remaining_time)}")

                            time.sleep(1.5)
                        except Exception as parent_error:
                            print(f"❌ All click attempts failed for {username}: {str(parent_error)}")
                else:
                    print(f"Already hidden: {username}")
            else:
                print(f"✅ Allowed (skipped): {username}")

        except Exception as e:
            print(f"❌ Error processing user {username if 'username' in locals() else 'unknown'}: {str(e)}")
            continue

except Exception as e:
    print(f"❌ Fatal error during processing: {str(e)}")

# Final summary
end_time = time.time()
total_elapsed = end_time - start_time
print(f"\n" + "="*60)
print(f"📊 FINAL SUMMARY")
print(f"="*60)
print(f"👥 Total users found: {len(user_buttons)}")
print(f"👤 Total users processed: {processed_count}")
print(f"🚫 Users hidden from stories: {hidden_count}")
print(f"✅ Users allowed (skipped): {len(allowed_usernames)}")
print(f"⏱️ Total time taken: {format_time(total_elapsed)}")
print(f"⚡ Average time per user: {total_elapsed/processed_count:.1f}s" if processed_count > 0 else "⚡ Average time per user: N/A")
print(f"🎯 Completion rate: {(hidden_count/total_users_to_process)*100:.1f}%" if total_users_to_process > 0 else "🎯 Completion rate: N/A")
print(f"="*60)

# Keep browser open for inspection
try:
    input("Press Enter to quit...")
except KeyboardInterrupt:
    print("\n🛑 Script interrupted by user")
finally:
    try:
        driver.quit()
        print("✅ Browser closed successfully")
    except Exception as e:
        print(f"❌ Error closing browser: {str(e)}")

# Style reference for checkbox states:
# Unchecked: style=mask-image: url("https://i.instagram.com/static/images/bloks/icons/generated/circle__outline__24-4x.png/2f71074dce25.png"); mask-size: contain; background-color: rgb(54, 54, 54); flex-shrink: 0; height: 24px; width: 24px;
# Checked: style=mask-image: url("https://i.instagram.com/static/images/bloks/icons/generated/circle-check__filled__24-4x.png/219f67ac4c95.png"); mask-size: contain; background-color: rgb(0, 149, 246); flex-shrink: 0; height: 24px; width: 24px;