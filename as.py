
import time

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

# --- Config ---
USERNAME = "uvais_mohammad___"
PASSWORD = "0000bavu****"

# --- Setup driver ---
chrome_options = webdriver.ChromeOptions()
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")
chrome_options.add_argument("--disable-blink-features=AutomationControlled")
chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
chrome_options.add_experimental_option('useAutomationExtension', False)

driver = webdriver.Chrome(options=chrome_options)
driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
driver.get("https://www.instagram.com/")

wait = WebDriverWait(driver, 20)

# --- Login ---
user_input = wait.until(EC.presence_of_element_located((By.NAME, "username")))
user_input.send_keys(USERNAME)

pass_input = driver.find_element(By.NAME, "password")
pass_input.send_keys(PASSWORD)
pass_input.send_keys(Keys.RETURN)

# Wait for homepage to load
time.sleep(5)
print("✅ Logged in successfully!")

# --- Navigate to Privacy & Security ---
driver.get("https://www.instagram.com/accounts/hide_story_and_live_from/")
wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

# --- List of usernames to allow (DO NOT hide story from these) ---
allowed_usernames = ["fath_wafaa_"]

SCROLL_PAUSE_TIME = 2
last_height = driver.execute_script("return document.body.scrollHeight")
print("last_height: ", last_height)
while True:
    print("Scrolling...")
    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
    print("last_height: ", last_height)
    time.sleep(SCROLL_PAUSE_TIME)
    new_height = driver.execute_script("return document.body.scrollHeight")
    print("new_height: ", new_height)
    if new_height == last_height:
        break
    last_height = new_height

user_buttons = driver.find_elements(By.XPATH, "//div[contains(@style, 'pointer-events: auto; width: 100%; margin-left: 4px; margin-right: 4px; cursor: pointer; -webkit-tap-highlight-color: transparent; align-items: center; flex-direction: row; justify-content: space-between;')]")   # Grabs username elements
print("user_buttons: ", user_buttons)
toggles = driver.find_elements(By.XPATH, "//div[contains(@aria-label, 'Toggle checkbox')]")
print("toggles: ", toggles)
# You may need to update the XPaths above by inspecting the DOM.
# Pair usernames and toggles (assuming same order)
processed_count = 0
try:
    for username_elem, toggle in zip(user_buttons, toggles):
        try:
            username = username_elem.text.strip()
            print(f"Processing user: {username} (Count: {processed_count + 1})")

            if username not in allowed_usernames:
                # Check the style attribute to determine if checkbox is checked or unchecked
                style_attr = toggle.get_attribute("style")

                # Style patterns for checked and unchecked states
                unchecked_pattern = 'mask-image: url("https://i.instagram.com/static/images/bloks/icons/generated/circle__outline__24-4x.png/2f71074dce25.png"); mask-size: contain; background-color: rgb(54, 54, 54); flex-shrink: 0; height: 24px; width: 24px;'
                checked_pattern = 'mask-image: url("https://i.instagram.com/static/images/bloks/icons/generated/circle-check__filled__24-4x.png/219f67ac4c95.png"); mask-size: contain; background-color: rgb(0, 149, 246); flex-shrink: 0; height: 24px; width: 24px;'

                # Determine if checkbox is checked based on style
                is_checked = "circle-check__filled" in style_attr or "rgb(0, 149, 246)" in style_attr

                # Click toggle only if not already checked
                if not is_checked:
                    # Wait for element to be clickable
                    wait.until(EC.element_to_be_clickable(toggle))
                    toggle.click()
                    print(f"✅ Hid story from: {username}")
                    # Add small delay after each click to prevent overwhelming Instagram
                    time.sleep(1)
                else:
                    print(f"Already hidden: {username}")
            else:
                print(f"Allowed: {username}")

            processed_count += 1

        except Exception as e:
            print(f"❌ Error processing user {username if 'username' in locals() else 'unknown'}: {str(e)}")
            continue

except Exception as e:
    print(f"❌ Fatal error during processing: {str(e)}")

print(f"\n📊 Total users processed: {processed_count}")

# Keep browser open for inspection
input("Press Enter to quit...")
driver.quit()

# Style reference for checkbox states:
# Unchecked: style=mask-image: url("https://i.instagram.com/static/images/bloks/icons/generated/circle__outline__24-4x.png/2f71074dce25.png"); mask-size: contain; background-color: rgb(54, 54, 54); flex-shrink: 0; height: 24px; width: 24px;
# Checked: style=mask-image: url("https://i.instagram.com/static/images/bloks/icons/generated/circle-check__filled__24-4x.png/219f67ac4c95.png"); mask-size: contain; background-color: rgb(0, 149, 246); flex-shrink: 0; height: 24px; width: 24px;